package vn.osp.historychanges.application.commands;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.application.commands.BaseCommand;
import vn.osp.common.application.commands.BaseCommandUpdateHandler;
import vn.osp.common.application.commands.UpdateCommand;
import vn.osp.common.domain.exceptions.EntityNotFoundException;
import vn.osp.historychanges.domain.entities.HistoryChanges;
import vn.osp.historychanges.domain.enums.ChangeAction;
import vn.osp.historychanges.domain.repositories.HistoryChangesRepository;

/** Command để cập nhật bản ghi lịch sử thay đổi. */
@Getter
@Setter
public class UpdateHistoryChangesCommand extends BaseCommand<Integer> {

  //  /** ID của bản ghi cần cập nhật */
  //  @Schema(hidden = true)
  //  private UUID id;
  //
  //  /** Row version để kiểm tra concurrency */
  //  @Schema(hidden = true)
  //  private Integer rowVersion;

  /** Dữ liệu liên quan đến action (JSON format) */
  private String data;

  /** Loại hành động */
  @NotNull(message = "Action không được để trống")
  private ChangeAction action;

  /** Mã dịch vụ mà bản ghi này cần liên kết khóa ngoại đến */
  @NotNull(message = "ServiceCode không được để trống")
  @Size(max = 100, message = "ServiceCode không được vượt quá 100 ký tự")
  private String serviceCode;

  /** Tên bảng cần liên kết khóa ngoại đến */
  @NotNull(message = "EntityCode không được để trống")
  @Size(max = 100, message = "EntityCode không được vượt quá 100 ký tự")
  private String entityCode;

  /** Id của bản ghi cần liên kết */
  private UUID entityId;
}

@Component
class UpdateHistoryChangesHandler
    extends BaseCommandUpdateHandler<UpdateHistoryChangesCommand, HistoryChanges, UUID> {

  public UpdateHistoryChangesHandler(HistoryChangesRepository repository) {
    super(repository);
  }

  /** Luôn define hàm này để Spring nhận diện đúng command */
  @EventListener
  @Transactional
  public void handle(UpdateCommand<UpdateHistoryChangesCommand, UUID> command) {
    super.handle(command);
  }

  @Override
  protected void validate(UpdateCommand<UpdateHistoryChangesCommand, UUID> command) {
    HistoryChanges entity = genericRepository.findById(command.getId());
    if (entity == null) {
      throw new EntityNotFoundException(HistoryChanges.class.getSimpleName(), command.getId());
    }
  }
}
