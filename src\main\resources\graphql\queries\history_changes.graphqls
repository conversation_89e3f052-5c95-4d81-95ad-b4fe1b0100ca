# HistoryChanges GraphQL Schema

enum ChangeAction {
  ENTITY_CREATE
  ENTITY_UPDATE
  ENTITY_DELETE
}

input ChangeActionOperationFilterInput {
  eq: ChangeAction
  neq: ChangeAction
  in: [ChangeAction!]
  nin: [ChangeAction!]
}

type HistoryChanges {
  id: UUID!
  data: String
  action: ChangeAction!
  serviceCode: String!
  entityCode: String!
  entityId: UUID
  createdAt: String!
  createdBy: UUID!
  modifiedAt: String!
  modifiedBy: UUID!
  rowVersion: Int!
}

input HistoryChangesFilterInput {
  and: [HistoryChangesFilterInput!]
  or: [HistoryChangesFilterInput!]
  id: UuidOperationFilterInput
  data: StringOperationFilterInput
  action: ChangeActionOperationFilterInput
  serviceCode: StringOperationFilterInput
  entityCode: StringOperationFilterInput
  entityId: UuidOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: UuidOperationFilterInput
  modifiedAt: DateTimeOperationFilterInput
  modifiedBy: UuidOperationFilterInput
}

input HistoryChangesSortInput {
  id: SortEnumType
  data: SortEnumType
  action: SortEnumType
  serviceCode: SortEnumType
  entityCode: SortEnumType
  entityId: SortEnumType
  createdAt: SortEnumType
  createdBy: SortEnumType
  modifiedAt: SortEnumType
  modifiedBy: SortEnumType
}

type HistoryChangesCollectionSegment {
  pageInfo: CollectionSegmentInfo!
  items: [HistoryChanges!]!
  totalCount: Int!
}