package vn.osp.historychanges.api.graphql;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;
import vn.osp.common.api.graphql.schema.CollectionSegment;
import vn.osp.common.domain.enums.SortDirection;
import vn.osp.historychanges.application.queries.GetHistoryChangesByIdQuery;
import vn.osp.historychanges.application.queries.GetHistoryChangesQuery;
import vn.osp.historychanges.domain.entities.HistoryChanges;

/**
 * GraphQL Resolver cho HistoryChanges.
 * <PERSON><PERSON> cấp các query để truy vấn dữ liệu lịch sử thay đổi.
 */
@Controller
public class HistoryChangesResolver {

  @Autowired protected ApplicationEventPublisher publisher;

  /**
   * Query lấy HistoryChanges theo ID.
   *
   * @param id ID của bản ghi cần lấy
   * @return HistoryChanges entity
   */
  @QueryMapping
  public HistoryChanges historyChanges(@Argument UUID id) {
    GetHistoryChangesByIdQuery query = new GetHistoryChangesByIdQuery(id);
    publisher.publishEvent(query);
    return query.getResult().join();
  }

  /**
   * Query lấy danh sách HistoryChanges với pagination, filter và sorting.
   *
   * @param skip Số bản ghi bỏ qua (pagination)
   * @param take Số bản ghi lấy về (pagination)
   * @param where Điều kiện filter
   * @param order Điều kiện sắp xếp
   * @return CollectionSegment chứa danh sách HistoryChanges và thông tin pagination
   */
  @QueryMapping
  public CollectionSegment<HistoryChanges> historyChangesList(
      @Argument Integer skip,
      @Argument Integer take,
      @Argument Map<String, Object> where,
      @Argument List<Map<String, SortDirection>> order) {
    
    Map<String, SortDirection> orderFlat = null;
    if (order != null) {
      orderFlat =
          order.stream()
              .flatMap(map -> map.entrySet().stream())
              .filter(entry -> entry.getValue() != null)
              .collect(
                  LinkedHashMap::new,
                  (m, e) -> m.put(e.getKey(), SortDirection.valueOf(e.getValue().toString())),
                  LinkedHashMap::putAll);
    }

    GetHistoryChangesQuery query = new GetHistoryChangesQuery(skip, take, where, orderFlat);
    publisher.publishEvent(query);
    return query.getResult().join();
  }
}