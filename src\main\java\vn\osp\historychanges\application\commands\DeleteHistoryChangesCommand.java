package vn.osp.historychanges.application.commands;

import java.util.UUID;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.application.commands.BaseCommandDeleteHandler;
import vn.osp.common.application.commands.DeleteCommand;
import vn.osp.historychanges.domain.entities.HistoryChanges;
import vn.osp.historychanges.domain.repositories.HistoryChangesRepository;

/** Command để xóa bản ghi lịch sử thay đổi. */
public class DeleteHistoryChangesCommand extends DeleteCommand<UUID> {

  public DeleteHistoryChangesCommand(UUID id, int rowVersion) {
    super(id, rowVersion);
  }
}

/** Handler xử lý command xóa bản ghi lịch sử thay đổi. */
@Component
class DeleteHistoryChangesHandler
    extends BaseCommandDeleteHandler<DeleteHistoryChangesCommand, HistoryChanges, UUID> {

  public DeleteHistoryChangesHandler(HistoryChangesRepository historyChangesRepository) {
    super(historyChangesRepository);
  }

  /** Luôn define hàm này để Spring nhận diện đúng command */
  @EventListener
  @Transactional
  public void handle(DeleteHistoryChangesCommand command) {
    super.handle(command);
  }
}
