type ExampleEntity {
  id: UUID!
  title: String!
  link: String!
  idPath: String
  activeLink: String
  parentId: UUID
  icon: String
  appCode: String!
  isVisible: Boolean!
  order: Int!
  requiredRoles: String
  children: [ExampleEntity!]!
  rowVersion: Int!
  createdAt: String!
  createdBy: UUID!
  modifiedAt: String!
  modifiedBy: UUID!
}

input ExampleEntityFilterInput {
  and: [ExampleEntityFilterInput!]
  or: [ExampleEntityFilterInput!]
  id: UuidOperationFilterInput
  title: StringOperationFilterInput
  link: StringOperationFilterInput
  idPath: StringOperationFilterInput
  activeLink: StringOperationFilterInput
  parentId: UuidOperationFilterInput
  appCode: StringOperationFilterInput
  isVisible: BooleanOperationFilterInput
  requiredRoles: StringOperationFilterInput
}

input ExampleEntitySortInput {
  title: SortEnumType
  appCode: SortEnumType
  isVisible: SortEnumType
  order: SortEnumType
  createdAt: SortEnumType
  modifiedAt: SortEnumType
}

type ExampleEntitiesCollectionSegment {
  pageInfo: CollectionSegmentInfo!
  items: [ExampleEntity!]!
  totalCount: Int!
}
