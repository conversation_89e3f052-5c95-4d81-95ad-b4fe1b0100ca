server:
  address: 0.0.0.0
  port: 8088

spring:
  application:
    name: osp-history-changes-service

  datasource:
    url: jdbc:postgresql://************:32000/nds_dev
    username: nds
    password: o3SqKqci8i9Fp39
    driver-class-name: org.postgresql.Driver

  jpa:
    properties:
      hibernate:
        default_schema: history_changes
        dialect: org.hibernate.dialect.PostgreSQLDialect
        ddl-auto: none   # ⚠️ Tắt Hibernate auto create/update schema

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    default-schema: history_changes
    enabled: false

  graphql:
    graphiql:
      enabled: true
      path: /graphiql
    http:
      path: /graphql

management:
  otlp:
    metrics:
      export:
        enabled: false

common:
  security:
    issuer-uri: https://sso.ospgroup.io.vn/realms/master
    mapping:
      id-claim: sub
      username-claim: preferred_username
      email-claim: email
      roles-claim: realm_access.roles