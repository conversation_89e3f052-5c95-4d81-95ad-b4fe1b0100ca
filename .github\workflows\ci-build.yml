name: CI Build

permissions:
  contents: read
  pull-requests: read

on:
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review, labeled, unlabeled ]

concurrency:
  group: ci-build-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Kiểm tra điều kiện để bỏ qua draft PR
  check-conditions:
    name: Check Build Conditions
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: Check if should build
        id: check
        run: |
          set -euo pipefail
          echo "🔍 Kiểm tra điều kiện build..."
          echo "Event: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Base Ref: ${{ github.base_ref }}"
          
          # CI build chỉ chạy trên PR events, không chạy trên push events
          # Điều này tr<PERSON>h duplicate builds khi merge PR vào main/develop
          
          # Chỉ xử lý PR events
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "🔀 Pull Request event detected"

            # Kiểm tra sự tồn tại của PR object
            if [[ -z "${{ github.event.pull_request.number }}" ]]; then
              echo "❌ Event type is 'pull_request' but no PR data found."
              echo "This could be due to an unsupported event action (e.g., 'closed')."
              echo "Skipping build to prevent errors."
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi

            echo "PR Draft Status: ${{ github.event.pull_request.draft }}"
            echo "Target Branch: ${{ github.event.pull_request.base.ref }}"
            
            # Kiểm tra draft status trước
            if [[ "${{ github.event.pull_request.draft }}" == "true" ]]; then
              echo "📝 Draft PR detected - bỏ qua build"
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi
            
            # Kiểm tra target branch
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            echo "🎯 Analyzing target branch: $TARGET_BRANCH"
            
            if [[ "$TARGET_BRANCH" == "main" || "$TARGET_BRANCH" == "develop" ]]; then
              echo "✅ PR targeting protected branch ($TARGET_BRANCH) - bắt buộc chạy build"
              echo "should-build=true" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "🔍 PR targeting other branch ($TARGET_BRANCH) - kiểm tra label 'ci-build'"
              
              # Kiểm tra label ci-build
              LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
              echo "Labels: $LABELS"
              
              if echo "$LABELS" | grep -q "ci-build"; then
                echo "🏷️ Label 'ci-build' được tìm thấy - sẽ chạy build"
                echo "should-build=true" >> $GITHUB_OUTPUT
              else
                echo "⏭️ Không có label 'ci-build' - bỏ qua build"
                echo "should-build=false" >> $GITHUB_OUTPUT
              fi
              exit 0
            fi
          fi
          
          # Chỉ chạy trên PR events, các event khác bỏ qua
          echo "⚠️ Event type không được hỗ trợ: ${{ github.event_name }}"
          echo "⏭️ CI Build is only triggered for pull_request events. Supported event types: [opened, synchronize, reopened, ready_for_review, labeled, unlabeled]."
          echo "should-build=false" >> $GITHUB_OUTPUT

  skip-build-notification:
    name: Skip Build Notification
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'false'
    
    steps:
      - name: Notify Skip Build
        run: |
          echo "⏭️ CI Build được bỏ qua"
          echo ""
          echo "📋 Điều kiện build:"
          echo "✅ PR vào main/develop: Luôn chạy build"
          echo "🏷️ PR vào branch khác: Cần label 'ci-build'"
          echo "📝 Draft PR: Luôn bỏ qua"
          echo ""
          echo "💡 Tips:"
          echo "- Thêm label 'ci-build' vào PR để kích hoạt build"
          echo "- Chuyển PR sang 'Ready for Review' nếu đang ở draft"
          echo "✅ Workflow hoàn thành thành công (skipped)"

  detect-changes:
    name: Detect File Changes
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      backend-changed: ${{ steps.changes.outputs.backend }}
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      business-docs-changed: ${{ steps.changes.outputs.business-docs }}
      meta-docs-changed: ${{ steps.changes.outputs.meta-docs }}
      requirements-review-changed: ${{ steps.changes.outputs.requirements-review }}
      ci-changed: ${{ steps.changes.outputs.ci }}
      pure-requirements-review: ${{ steps.pure-review-check.outputs.pure_requirements_review }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: Detect file changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            backend:
              - 'src/backend/**'
              - 'docs/backend/**'
            frontend:
              - 'src/frontend/**'
              - 'docs/frontend/**'
            business-docs:
              - 'docs/requirements/**'
              - '!docs/requirements/manual-trigger-test.md'
              - '!docs/requirements/*-test.md'
              - 'docs/architecture/**'
              - 'docs/software-design/**'
              - 'docs/detailed-design/**'
            meta-docs:
              - 'docs/guidelines/**'
              - 'docs/issues/**'
              - 'docs/*.md'
            requirements-review:
              - 'docs/requirements/manual-trigger-test.md'
              - 'docs/requirements/*-test.md'
              - '.github/workflows/requirements-change-review.yml'
              - '.github/actions/requirements-review/**'
            ci:
              - '.github/workflows/**'
              - '!.github/workflows/requirements-change-review.yml'
      
      - name: Check pure requirements review
        id: pure-review-check
        run: |
          PURE_REVIEW="false"
          if [[ "${{ steps.changes.outputs['requirements-review'] }}" == "true" &&
                "${{ steps.changes.outputs.backend }}" == "false" &&
                "${{ steps.changes.outputs.frontend }}" == "false" &&
                "${{ steps.changes.outputs['business-docs'] }}" == "false" ]]; then
            PURE_REVIEW="true"
            echo "🔍 Detected pure requirements review workflow changes - builds will be skipped"
          fi
          echo "pure_requirements_review=${PURE_REVIEW}" >> $GITHUB_OUTPUT

      - name: Log detected changes
        run: |
          echo "🔍 File Changes Detection Results:"
          echo "Backend changes: ${{ steps.changes.outputs.backend }}"
          echo "Frontend changes: ${{ steps.changes.outputs.frontend }}"
          echo "Business docs changes: ${{ steps.changes.outputs['business-docs'] }}"
          echo "Meta docs changes: ${{ steps.changes.outputs['meta-docs'] }}"
          echo "Requirements review changes: ${{ steps.changes.outputs['requirements-review'] }}"
          echo "CI changes: ${{ steps.changes.outputs.ci }}"
          echo "Pure requirements review: ${{ steps['pure-review-check'].outputs.pure_requirements_review }}"

  build-backend:
    name: Build Backend (Java Spring Boot)
    uses: ./.github/workflows/reusable-flexible-runner.yml
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.backend-changed == 'true'
    with:
      job_name: 'Build Backend (Java Spring Boot)'
      setup_java: true
      java_version: '21'
      working_directory: 'src/backend'
      install_dependencies: 'backend'
      run_command: |
        echo "🔨 Building Backend - Reasons:"
        echo "Backend changes: ${{ needs.detect-changes.outputs['backend-changed'] }}"
        echo "Business docs changes: ${{ needs.detect-changes.outputs['business-docs-changed'] }}"
        echo "---"
        echo "🔨 Building Backend Spring Boot project..."
        mvn -B clean verify
        echo "✅ Backend build completed successfully!"

  build-frontend:
    name: Build Frontend (NextJS TypeScript)
    uses: ./.github/workflows/reusable-flexible-runner.yml
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.frontend-changed == 'true'
    with:
      job_name: 'Build Frontend (NextJS TypeScript)'
      setup_node: true
      node_version: '20'
      setup_pnpm: true
      pnpm_version: '10'
      working_directory: 'src/frontend'
      install_dependencies: 'frontend'
      run_command: |
        echo "🔨 Building Frontend - Reasons:"
        echo "Frontend changes: ${{ needs.detect-changes.outputs['frontend-changed'] }}"
        echo "Business docs changes: ${{ needs.detect-changes.outputs['business-docs-changed'] }}"
        echo "---"
        echo "🔨 Building Frontend NextJS projects..."
        pnpm build
        echo "✅ Frontend build completed successfully!"

  build-status:
    name: Build Status Summary
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-backend, build-frontend]
    if: always() && needs.check-conditions.outputs.should-build == 'true'
    
    steps:
      - name: Smart Build Summary
        run: |
          echo "📊 Path-Based Selective Build Results:"
          echo ""
          echo "🔍 File Changes Detection:"
          echo "  Backend: ${{ needs.detect-changes.outputs['backend-changed'] }}"
          echo "  Frontend: ${{ needs.detect-changes.outputs['frontend-changed'] }}"
          echo "  Business Docs: ${{ needs.detect-changes.outputs['business-docs-changed'] }}"
          echo "  Meta Docs: ${{ needs.detect-changes.outputs['meta-docs-changed'] }}"
          echo "  Requirements Review: ${{ needs.detect-changes.outputs['requirements-review-changed'] }}"
          echo "  CI: ${{ needs.detect-changes.outputs['ci-changed'] }}"
          echo "  Pure Requirements Review: ${{ needs.detect-changes.outputs['pure-requirements-review'] }}"
          echo ""
          echo "📋 Build Trigger Conditions:"
          echo "  Backend builds when: backend-changed == true"
          echo "  Frontend builds when: frontend-changed == true"
          echo ""

          # Check if pure requirements review
          if [[ "${{ needs.detect-changes.outputs['pure-requirements-review'] }}" == "true" ]]; then
            echo "🔍 Pure Requirements Review Detected:"
            echo "⏭️ Backend Build: Skipped (pure requirements review workflow)"
            echo "⏭️ Frontend Build: Skipped (pure requirements review workflow)"
            echo ""
            echo "💡 This appears to be a requirements review workflow trigger."
            echo "   No code builds are needed for pure workflow/documentation changes."
            BACKEND_SHOULD_BUILD="false"
            FRONTEND_SHOULD_BUILD="false"
          else
            # Backend status - khớp với điều kiện thực tế của build-backend job
            BACKEND_SHOULD_BUILD="false"
            if [[ "${{ needs.detect-changes.outputs['backend-changed'] }}" == "true" ]]; then
              BACKEND_SHOULD_BUILD="true"
              echo "🔨 Backend Build: ${{ needs.build-backend.result }}"
            else
              echo "⏭️ Backend Build: Skipped (no backend changes detected)"
            fi

            # Frontend status - khớp với điều kiện thực tế của build-frontend job
            FRONTEND_SHOULD_BUILD="false"
            if [[ "${{ needs.detect-changes.outputs['frontend-changed'] }}" == "true" ]]; then
              FRONTEND_SHOULD_BUILD="true"
              echo "🔨 Frontend Build: ${{ needs.build-frontend.result }}"
            else
              echo "⏭️ Frontend Build: Skipped (no frontend changes detected)"
            fi
          fi
          
          echo ""
          
          # Check overall success
          OVERALL_SUCCESS="true"
          
          if [[ "$BACKEND_SHOULD_BUILD" == "true" && "${{ needs.build-backend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Backend build was required but failed!"
          fi
          
          if [[ "$FRONTEND_SHOULD_BUILD" == "true" && "${{ needs.build-frontend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Frontend build was required but failed!"
          fi
          
          if [[ "$OVERALL_SUCCESS" == "true" ]]; then
            echo "🎉 All required builds completed successfully!"
            echo "💡 Path-based selective building optimized CI time and resources!"
          else
            echo "💥 One or more required builds failed!"
            exit 1
          fi

  no-changes-notification:
    name: No Changes Notification
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.backend-changed == 'false' &&
      needs.detect-changes.outputs.frontend-changed == 'false' &&
      needs.detect-changes.outputs.ci-changed == 'false' &&
      needs.detect-changes.outputs.business-docs-changed == 'false' &&
      needs.detect-changes.outputs.meta-docs-changed == 'false' &&
      needs.detect-changes.outputs.requirements-review-changed == 'false'
    
    steps:
      - name: Notify No Relevant Changes
        run: |
          echo "🔍 Path-Based Analysis: No Relevant Changes Detected"
          echo ""
          echo "📁 Changed paths analysis showed no files in:"
          echo "  ❌ src/backend/**"
          echo "  ❌ src/frontend/**"  
          echo "  ❌ docs/requirements/** (business docs)"
          echo "  ❌ docs/architecture/** (business docs)"
          echo "  ❌ docs/software-design/** (business docs)"
          echo "  ❌ docs/detailed-design/** (business docs)"
          echo "  ❌ docs/guidelines/** (meta docs)"
          echo "  ❌ docs/issues/** (meta docs)"
          echo "  ❌ .github/workflows/**"
          echo ""
          echo "⏭️ Both Frontend and Backend builds skipped"
          echo "💡 This optimization saved significant CI time and resources!"
          echo "✅ Workflow completed successfully (selective skip)"
