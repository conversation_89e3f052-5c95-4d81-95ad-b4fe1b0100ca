package vn.osp.historychanges.application.queries;

import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import vn.osp.common.application.queries.BaseGetByIdQueryHandler;
import vn.osp.common.application.queries.GetByIdQuery;
import vn.osp.common.domain.repositories.GenericRepository;
import vn.osp.historychanges.domain.entities.HistoryChanges;

/** Query để lấy bản ghi HistoryChanges theo ID. */
@Getter
@Setter
public class GetHistoryChangesByIdQuery extends GetByIdQuery<HistoryChanges, UUID> {

  public GetHistoryChangesByIdQuery(UUID id) {
    super(id);
  }
}

/** <PERSON>ler xử lý query lấy HistoryChanges theo ID. */
@Component
class GetHistoryChangesByIdHandler
    extends BaseGetByIdQueryHandler<GetHistoryChangesByIdQuery, HistoryChanges, UUID> {

  public GetHistoryChangesByIdHandler(GenericRepository<HistoryChanges, UUID> genericRepository) {
    super(genericRepository);
  }

  /** Luôn define hàm này để Spring nhận diện đúng query */
  @EventListener
  public void handle(GetHistoryChangesByIdQuery query) {
    super.handle(query);
  }
}
