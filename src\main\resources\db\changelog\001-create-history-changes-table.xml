<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog 
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet id="001-create-history-changes-table" author="system">
        <createTable tableName="HistoryChanges">
            <!-- Primary Key -->
            <column name="Id" type="UUID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            
            <!-- Business Fields -->
            <column name="Data" type="TEXT">
                <constraints nullable="true"/>
            </column>
            
            <column name="Action" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            
            <column name="ServiceCode" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            
            <column name="EntityCode" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            
            <column name="EntityId" type="UUID">
                <constraints nullable="true"/>
            </column>
            
            <!-- Audit Fields -->
            <column name="CreatedAt" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            
            <column name="CreatedBy" type="UUID">
                <constraints nullable="false"/>
            </column>
            
            <column name="ModifiedAt" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            
            <column name="ModifiedBy" type="UUID">
                <constraints nullable="false"/>
            </column>

            <column name="RowVersion" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false" />
            </column>
        </createTable>
        
        <!-- Add indexes for performance -->
        <createIndex tableName="HistoryChanges" indexName="idx_history_changes_service_entity">
            <column name="ServiceCode"/>
            <column name="EntityCode"/>
            <column name="EntityId" />
        </createIndex>
        
        <!-- Add comments -->
        <sql>
            COMMENT ON TABLE "HistoryChanges" IS 'Bảng lưu trữ lịch sử thay đổi của các entity trong hệ thống';
            COMMENT ON COLUMN "HistoryChanges"."Id" IS 'Khóa chính của bảng';
            COMMENT ON COLUMN "HistoryChanges"."Data" IS 'Dữ liệu liên quan đến action (JSON format)';
            COMMENT ON COLUMN "HistoryChanges"."Action" IS 'Loại hành động (CREATE, UPDATE, DELETE)';
            COMMENT ON COLUMN "HistoryChanges"."ServiceCode" IS 'Mã dịch vụ mà bản ghi này cần liên kết khóa ngoại đến';
            COMMENT ON COLUMN "HistoryChanges"."EntityCode" IS 'Tên bảng cần liên kết khóa ngoại đến';
            COMMENT ON COLUMN "HistoryChanges"."EntityId" IS 'Id của bản ghi cần liên kết';
            COMMENT ON COLUMN "HistoryChanges"."CreatedAt" IS 'Thời gian tạo bản ghi';
            COMMENT ON COLUMN "HistoryChanges"."CreatedBy" IS 'Id người tạo bản ghi';
            COMMENT ON COLUMN "HistoryChanges"."ModifiedAt" IS 'Thời gian cập nhật bản ghi';
            COMMENT ON COLUMN "HistoryChanges"."ModifiedBy" IS 'Id người cập nhật bản ghi';
        </sql>
    </changeSet>

</databaseChangeLog>