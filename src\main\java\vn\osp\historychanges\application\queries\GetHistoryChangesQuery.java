package vn.osp.historychanges.application.queries;

import java.util.Map;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import vn.osp.common.application.queries.BasePaginateQueryHandler;
import vn.osp.common.application.queries.PaginateQuery;
import vn.osp.common.domain.enums.SortDirection;
import vn.osp.historychanges.domain.entities.HistoryChanges;
import vn.osp.historychanges.domain.repositories.HistoryChangesRepository;

/** Query để lấy danh sách HistoryChanges với pagination và filter. */
@Getter
@Setter
public class GetHistoryChangesQuery extends PaginateQuery<HistoryChanges> {

  public GetHistoryChangesQuery(
      Integer skip, Integer take, Map<String, Object> where, Map<String, SortDirection> order) {
    super(skip, take, where, order);
  }
}

/** <PERSON><PERSON> xử lý query lấy danh sách HistoryChanges với pagination. */
@Component
class GetHistoryChangesHandler
    extends BasePaginateQueryHandler<GetHistoryChangesQuery, HistoryChanges, UUID> {

  public GetHistoryChangesHandler(HistoryChangesRepository genericRepository) {
    super(genericRepository);
  }

  /** Luôn define hàm này để Spring nhận diện đúng query */
  @EventListener
  public void handle(GetHistoryChangesQuery query) {
    super.handle(query);
  }
}
