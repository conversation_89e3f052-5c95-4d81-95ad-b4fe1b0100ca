package vn.osp.historychanges.infrastructure.repositories;

import java.util.UUID;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import vn.osp.common.application.contexts.RequestContext;
import vn.osp.common.infrastructure.repositories.GenericRepositoryImpl;
import vn.osp.historychanges.domain.entities.ExampleEntity;
import vn.osp.historychanges.domain.repositories.ExampleEntityRepository;

@Component
public class ExampleEntityRepositoryImpl extends GenericRepositoryImpl<ExampleEntity, UUID>
    implements ExampleEntityRepository {

  protected ExampleEntityRepositoryImpl(RequestContext requestContext, ModelMapper modelMapper) {
    super(ExampleEntity.class, requestContext, modelMapper);
  }
}
