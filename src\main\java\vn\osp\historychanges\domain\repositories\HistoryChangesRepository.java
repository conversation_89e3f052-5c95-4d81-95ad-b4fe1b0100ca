package vn.osp.historychanges.domain.repositories;

import java.util.UUID;
import vn.osp.common.domain.repositories.GenericRepository;
import vn.osp.historychanges.domain.entities.HistoryChanges;

/**
 * Repository cho thực thể HistoryChanges.
 * <PERSON><PERSON> thừa các phương thức thao tác dữ liệu từ GenericRepository.
 */
public interface HistoryChangesRepository extends GenericRepository<HistoryChanges, UUID> {
}