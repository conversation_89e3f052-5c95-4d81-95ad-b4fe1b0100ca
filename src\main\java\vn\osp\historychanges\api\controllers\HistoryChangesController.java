package vn.osp.historychanges.api.controllers;

import jakarta.validation.Valid;
import java.util.UUID;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.osp.common.api.controllers.BaseRestApi;
import vn.osp.historychanges.application.commands.CreateHistoryChangesCommand;
import vn.osp.historychanges.application.commands.DeleteHistoryChangesCommand;
import vn.osp.historychanges.application.commands.UpdateHistoryChangesCommand;

/**
 * REST API Controller cho HistoryChanges. Cung cấp các endpoint để thao tác với lịch sử thay đổi.
 */
@RestController
@RequestMapping("/api/v1/history-changes")
public class HistoryChangesController extends BaseRestApi {

  /**
   * Tạo mới bản ghi lịch sử thay đổi.
   *
   * @param command Command chứa thông tin tạo mới
   * @return ResponseEntity chứa ID của bản ghi được tạo
   */
  @PostMapping
  public ResponseEntity<UUID> post(@Valid @RequestBody CreateHistoryChangesCommand command) {
    return doPost(command);
  }

  /**
   * Cập nhật bản ghi lịch sử thay đổi.
   *
   * @param id ID của bản ghi cần cập nhật
   * @param rowVersion Row version để kiểm tra concurrency
   * @param command Command chứa thông tin cập nhật
   * @return ResponseEntity chứa row version mới
   */
  @PutMapping("/{id}/{rowVersion}")
  public ResponseEntity<Integer> put(
      @PathVariable UUID id,
      @PathVariable int rowVersion,
      @Valid @RequestBody UpdateHistoryChangesCommand command) {
    return doPut(id, rowVersion, command);
  }

  /**
   * Xóa bản ghi lịch sử thay đổi.
   *
   * @param id ID của bản ghi cần xóa
   * @param rowVersion Row version để kiểm tra concurrency
   * @return ResponseEntity rỗng
   */
  @DeleteMapping("/{id}/{rowVersion}")
  public ResponseEntity<Void> delete(@PathVariable UUID id, @PathVariable int rowVersion) {
    return doDelete(new DeleteHistoryChangesCommand(id, rowVersion));
  }
}
