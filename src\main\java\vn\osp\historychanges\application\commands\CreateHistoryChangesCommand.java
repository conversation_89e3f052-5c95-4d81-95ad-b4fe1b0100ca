package vn.osp.historychanges.application.commands;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.application.commands.BaseCommand;
import vn.osp.common.application.commands.BaseCommandCreateHandler;
import vn.osp.historychanges.domain.entities.HistoryChanges;
import vn.osp.historychanges.domain.enums.ChangeAction;
import vn.osp.historychanges.domain.repositories.HistoryChangesRepository;

/** Command để tạo mới bản ghi lịch sử thay đổi. */
@Getter
@Setter
public class CreateHistoryChangesCommand extends BaseCommand<UUID> {

  /** <PERSON><PERSON> liệu liên quan đến action (JSON format) */
  private String data;

  /** Loại hành động */
  @NotNull(message = "Action không được để trống")
  private ChangeAction action;

  /** Mã dịch vụ mà bản ghi này cần liên kết khóa ngoại đến */
  @NotNull(message = "ServiceCode không được để trống")
  @Size(max = 100, message = "ServiceCode không được vượt quá 100 ký tự")
  private String serviceCode;

  /** Tên bảng cần liên kết khóa ngoại đến */
  @NotNull(message = "EntityCode không được để trống")
  @Size(max = 100, message = "EntityCode không được vượt quá 100 ký tự")
  private String entityCode;

  /** Id của bản ghi cần liên kết */
  private UUID entityId;
}

/** Handler xử lý command tạo mới bản ghi lịch sử thay đổi. */
@Component
class CreateHistoryChangesHandler
    extends BaseCommandCreateHandler<CreateHistoryChangesCommand, HistoryChanges, UUID> {

  public CreateHistoryChangesHandler(
      HistoryChangesRepository historyChangesRepository, ModelMapper modelMapper) {
    super(historyChangesRepository, modelMapper, HistoryChanges.class);
  }

  /** Luôn define hàm này để Spring nhận diện đúng command */
  @EventListener
  @Transactional
  public void handle(CreateHistoryChangesCommand command) {
    super.handle(command);
  }
}
