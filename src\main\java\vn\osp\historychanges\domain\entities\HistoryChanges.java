package vn.osp.historychanges.domain.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.osp.common.domain.domainentitytypes.BaseDomainEntityUuid;
import vn.osp.common.domain.domainentitytypes.HasCreatedBy;
import vn.osp.common.domain.domainentitytypes.HasModifiedBy;
import vn.osp.historychanges.domain.enums.ChangeAction;

@Entity
@Table(name = "\"HistoryChanges\"")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class HistoryChanges extends BaseDomainEntityUuid implements HasCreatedBy, HasModifiedBy {
  /** Dữ liệu liên quan đến action */
  @Column(name = "\"Data\"")
  private String data;

  /** <PERSON>ại hành động */
  @Column(name = "\"Action\"", nullable = false)
  private ChangeAction action;

  /**
   * Mã dịch vụ mà bản ghi này cần liên kết khóa ngoại đến (thường áp dụng khi liên kết sang bảng
   * thuộc db khác)
   */
  @Column(name = "\"ServiceCode\"", nullable = false)
  private String serviceCode;

  /** Tên bảng cần liên kết khóa ngoại đến (thường áp dụng khi liên kết sang bảng thuộc db khác) */
  @Column(name = "\"EntityCode\"", nullable = false)
  private String entityCode;

  /**
   * Id của bản ghi cần liên kết (thường áp dụng khi liên kết sang bảng thuộc db khác) Cần có thêm
   * cả 2 thông tin ServiceCode và EntityCode
   */
  @Column(name = "\"EntityId\"")
  private UUID entityId;

  @Column(name = "\"CreatedAt\"", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "\"CreatedBy\"", nullable = false)
  private UUID createdBy;

  @Column(name = "\"ModifiedAt\"", nullable = false)
  private LocalDateTime modifiedAt;

  @Column(name = "\"ModifiedBy\"", nullable = false)
  private UUID modifiedBy;
}
