type Query {
  exampleEntities(
    skip: Int
    take: Int
    where: ExampleEntityFilterInput
    order: [ExampleEntitySortInput!]
  ): ExampleEntitiesCollectionSegment
  exampleEntity(id: UUID!): ExampleEntity
  
  historyChangesList(
    skip: Int
    take: Int
    where: HistoryChangesFilterInput
    order: [HistoryChangesSortInput!]
  ): HistoryChangesCollectionSegment
  historyChanges(id: UUID!): HistoryChanges
}
