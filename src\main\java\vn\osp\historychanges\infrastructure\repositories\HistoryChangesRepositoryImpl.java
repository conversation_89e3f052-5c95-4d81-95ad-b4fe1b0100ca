package vn.osp.historychanges.infrastructure.repositories;

import java.util.UUID;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;
import vn.osp.common.application.contexts.RequestContext;
import vn.osp.common.infrastructure.repositories.GenericRepositoryImpl;
import vn.osp.historychanges.domain.entities.HistoryChanges;
import vn.osp.historychanges.domain.repositories.HistoryChangesRepository;

/**
 * Implementation của HistoryChangesRepository sử dụng JPA.
 */
@Component
public class HistoryChangesRepositoryImpl extends GenericRepositoryImpl<HistoryChanges, UUID>
    implements HistoryChangesRepository {

  protected HistoryChangesRepositoryImpl(RequestContext requestContext, ModelMapper modelMapper) {
    super(HistoryChanges.class, requestContext, modelMapper);
  }
}