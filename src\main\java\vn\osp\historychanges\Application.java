package vn.osp.historychanges;

import java.util.logging.LogManager;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.slf4j.bridge.SLF4JBridgeHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/** Điểm khởi động ứng dụng Spring Boot. */
@SpringBootApplication(scanBasePackages = {"vn.osp.historychanges", "vn.osp.common"})
public class Application {

  /**
   * Hàm main khởi động ứng dụng. Thiết lập logging và chạy Spring Boot.
   *
   * @param args tham số dòng lệnh
   */
  public static void main(String[] args) {
    // Remove default handlers attached to JUL
    LogManager.getLogManager().reset();

    // Install SLF4J bridge
    SLF4JBridgeHandler.install();

    SpringApplication.run(Application.class, args);
  }

  /**
   * Bean cấu hình ModelMapper với MatchingStrategy nghiêm ngặt.
   *
   * @return ModelMapper đã cấu hình
   */
  @Bean
  ModelMapper modelMapper() {
    ModelMapper modelMapper = new ModelMapper();
    modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    return modelMapper;
  }
}
