input DateTimeOperationFilterInput {
    eq: DateTime
    neq: DateTime
    in: [DateTime]
    nin: [DateTime]
    gt: DateTime
    ngt: DateTime
    gte: DateTime
    ngte: DateTime
    lt: DateTime
    nlt: DateTime
    lte: DateTime
    nlte: DateTime
}

input IntOperationFilterInput {
    eq: Int
    neq: Int
    in: [Int]
    nin: [Int]
    gt: Int
    ngt: Int
    gte: Int
    ngte: Int
    lt: Int
    nlt: Int
    lte: Int
    nlte: Int
}

input StringOperationFilterInput {
    and: [StringOperationFilterInput!]
    or: [StringOperationFilterInput!]
    eq: String
    neq: String
    contains: String
    ncontains: String
    in: [String]
    nin: [String]
    startsWith: String
    nstartsWith: String
    endsWith: String
    nendsWith: String
}

input BooleanOperationFilterInput {
    eq: Boolean
    neq: Boolean
}

input UuidOperationFilterInput {
    eq: UUID
    neq: UUID
    in: [UUID]
    nin: [UUID]
}
