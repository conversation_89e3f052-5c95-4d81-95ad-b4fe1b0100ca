<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>vn.osp</groupId>
    <artifactId>osp-history-changes-service</artifactId>
    <version>0.0.1</version>
    <name>osp-history-changes-service</name>
    <description>D<PERSON><PERSON> v<PERSON> lưu tr<PERSON> lịch sử thay đ<PERSON>i của c<PERSON>c thực thể</description>
    <properties>
        <java.version>21</java.version>
        <!-- Spring Boot Version (inherited from parent) -->
        <spring-boot.version>3.5.5</spring-boot.version>
        <!-- OSP Common Module Version -->
        <osp-common.version>0.0.3</osp-common.version>
        <!-- Hibernate Version -->
        <hibernate-core.version>7.1.1.Final</hibernate-core.version>
        <!-- Jakarta Persistence API Version -->
        <jakarta-persistence-api.version>3.2.0</jakarta-persistence-api.version>
        <!-- Liquibase Version -->
        <liquibase-maven-plugin.version>4.31.1</liquibase-maven-plugin.version>
        <spotless-maven-plugin.version>2.46.1</spotless-maven-plugin.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Reference to common module -->
        <dependency>
            <groupId>vn.osp</groupId>
            <artifactId>common</artifactId>
            <version>${osp-common.version}</version>
        </dependency>

        <!-- Hibernate Core for Event Listeners and Interceptors -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate-core.version}</version>
        </dependency>

        <!-- JPA API (OptimisticLockException) -->
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
            <version>${jakarta-persistence-api.version}</version>
        </dependency>

        <!-- Spring Boot Data JPA Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- PostgreSQL Driver -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Liquibase Core -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <version>${liquibase-maven-plugin.version}</version>
                <configuration>
                    <propertyFile>src/main/resources/liquibase.properties</propertyFile>
                    <changeLogFile>src/main/resources/db/changelog/db.changelog-master.xml</changeLogFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless-maven-plugin.version}</version>
                <configuration>
                    <java>
                        <googleJavaFormat/>
                    </java>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>