# Test GraphQL Query cho HistoryChanges
query HistoryChangesList {
    historyChangesList(where: { action: { eq: ENTITY_CREATE } }) {
        pageInfo {
            hasNextPage
            hasPreviousPage
        }
        items {
            id
            data
            action
            serviceCode
            entityCode
            entityId
            createdAt
            createdBy
            modifiedAt
            modifiedBy
            rowVersion
        }
        totalCount
    }
}

# Query với pagination
query HistoryChangesListWithPagination {
    historyChangesList(
        skip: 0
        take: 10
        where: { action: { eq: ENTITY_CREATE } }
        order: [{ createdAt: DESC }]
    ) {
        pageInfo {
            hasNextPage
            hasPreviousPage
        }
        items {
            id
            action
            serviceCode
            entityCode
            createdAt
        }
        totalCount
    }
}

# Query với multiple filters
query HistoryChangesListWithMultipleFilters {
    historyChangesList(where: { 
        and: [
            { action: { eq: ENTITY_CREATE } }
            { serviceCode: { eq: "USER_SERVICE" } }
        ]
    }) {
        items {
            id
            action
            serviceCode
            entityCode
            createdAt
        }
        totalCount
    }
}
