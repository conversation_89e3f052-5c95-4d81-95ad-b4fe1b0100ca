package vn.osp.historychanges.domain.repositories;

import java.util.UUID;

import vn.osp.common.domain.repositories.GenericRepository;
import vn.osp.historychanges.domain.entities.ExampleEntity;

/**
 * Repository cho thực thể ExampleEntity. <PERSON><PERSON> thừa các phương thức thao tác dữ liệu từ
 * GenericRepository.
 */
public interface ExampleEntityRepository extends GenericRepository<ExampleEntity, UUID> {}
