name: Reusable Lark Notification

on:
  workflow_call:
    inputs:
      notification_type:
        description: '<PERSON><PERSON><PERSON> thông báo (pr, issue, requirements)'
        required: true
        type: string
      title:
        description: 'Tiêu đề thông báo'
        required: true
        type: string
      content:
        description: 'Nội dung thông báo'
        required: true
        type: string
      event_url:
        description: 'URL của event (PR/Issue)'
        required: true
        type: string
      event_number:
        description: 'Số PR/Issue'
        required: true
        type: string
      event_user:
        description: 'Người tạo PR/Issue'
        required: true
        type: string
      event_created_at:
        description: 'Thời gian tạo'
        required: true
        type: string
      additional_info:
        description: 'Thông tin bổ sung (JSON format)'
        required: false
        type: string
        default: '{}'
      header_template:
        description: 'Template màu header (blue, orange, red, green)'
        required: false
        type: string
        default: 'blue'
      skip_condition:
        description: 'Điều kiện để skip notification'
        required: false
        type: boolean
        default: false
    secrets:
      LARK_WEBHOOK_URL:
        description: 'Lark webhook URL'
        required: true

jobs:
  send-notification:
    name: Send Lark Notification
    runs-on: ubuntu-latest
    if: inputs.skip_condition == false
    
    steps:
      - name: Prepare notification data
        id: prepare
        run: |
          echo "🔔 Preparing Lark notification..."
          echo "Notification type: ${{ inputs.notification_type }}"
          echo "Event: ${{ github.event_name }}"
          echo "Repository: ${{ github.repository }}"

          # Extract additional info
          ADDITIONAL_INFO='${{ inputs.additional_info }}'
          echo "Additional info: $ADDITIONAL_INFO"

          # Validate and format timestamp (GMT+7 timezone)
          TIMESTAMP="${{ inputs.event_created_at }}"
          if [[ "$TIMESTAMP" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}[T\ ][0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
            FORMATTED_TIME=$(TZ='Asia/Ho_Chi_Minh' date -d "$TIMESTAMP" +"%d/%m/%Y %H:%M:%S" 2>/dev/null || echo "$TIMESTAMP")
          else
            echo "⚠️ Invalid timestamp format for event_created_at: '$TIMESTAMP'"
            FORMATTED_TIME="$TIMESTAMP"
          fi
          echo "formatted_time=$FORMATTED_TIME" >> $GITHUB_OUTPUT

          # Create project prefix
          PROJECT_CODE="${{ vars.PROJECT_CODE }}"
          if [[ -n "$PROJECT_CODE" ]]; then
            PROJECT_PREFIX="[OSP][$PROJECT_CODE]"
          else
            PROJECT_PREFIX="[OSP][UNKNOWN]"
            echo "⚠️ PROJECT_CODE not configured, using UNKNOWN"
          fi
          echo "project_prefix=$PROJECT_PREFIX" >> $GITHUB_OUTPUT

          # Workflow URL
          WORKFLOW_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          echo "workflow_url=$WORKFLOW_URL" >> $GITHUB_OUTPUT

      - name: Build notification content
        id: build-content
        run: |
          echo "🎨 Building notification content..."
          
          PROJECT_PREFIX="${{ steps.prepare.outputs.project_prefix }}"
          FORMATTED_TIME="${{ steps.prepare.outputs.formatted_time }}"
          WORKFLOW_URL="${{ steps.prepare.outputs.workflow_url }}"
          
          # Build event type based on notification type
          case "${{ inputs.notification_type }}" in
            "pr")
              EVENT_TYPE="$PROJECT_PREFIX [Pull Request] ${{ inputs.title }} [PR mới]"
              BASE_CONTENT="Pull Request Link: [#${{ inputs.event_number }}](${{ inputs.event_url }})<br />Tạo bởi: ${{ inputs.event_user }}<br />Tạo lúc: $FORMATTED_TIME"
              ACTION_CONTENT="📝 **Hành động cần thực hiện:**<br />• Review code changes<br />• Kiểm tra unit tests<br />• Đánh giá tác động<br />• Approve hoặc comment"
              ;;
            "issue")
              EVENT_TYPE="$PROJECT_PREFIX [Requirements] ${{ inputs.title }} [Thay đổi mới]"
              BASE_CONTENT="Issue Link: [#${{ inputs.event_number }}](${{ inputs.event_url }})<br />Tạo bởi: ${{ inputs.event_user }}<br />Tạo lúc: $FORMATTED_TIME"
              ACTION_CONTENT="📝 **Hành động cần thực hiện:**<br />• Đánh giá tác động thay đổi<br />• Phân tích rủi ro<br />• Cập nhật documentation<br />• Review implementation plan"
              ;;
            "requirements")
              EVENT_TYPE="$PROJECT_PREFIX [Requirements Review] ${{ inputs.title }}"
              BASE_CONTENT="Thời gian phát hiện: $FORMATTED_TIME<br />Người commit: ${{ inputs.event_user }}"
              ACTION_CONTENT="📝 **Hành động cần thực hiện:**<br />• Đánh giá tác động thay đổi<br />• Phân tích rủi ro<br />• Cập nhật các vai trò liên quan<br />• Review implementation plan"
              ;;
            *)
              EVENT_TYPE="$PROJECT_PREFIX [Notification] ${{ inputs.title }}"
              BASE_CONTENT="Event: ${{ inputs.event_url }}<br />Tạo bởi: ${{ inputs.event_user }}<br />Tạo lúc: $FORMATTED_TIME"
              ACTION_CONTENT="📝 **Hành động cần thực hiện:**<br />• Xem chi tiết<br />• Thực hiện action tương ứng"
              ;;
          esac

          # Parse additional info if provided
          ADDITIONAL_INFO='${{ inputs.additional_info }}'
          ADDITIONAL_CONTENT=""
          if [[ "$ADDITIONAL_INFO" != "{}" ]] && [[ -n "$ADDITIONAL_INFO" ]]; then
            # Extract specific fields from additional info
            if echo "$ADDITIONAL_INFO" | jq -e '.base_branch' > /dev/null 2>&1; then
              BASE_BRANCH=$(echo "$ADDITIONAL_INFO" | jq -r '.base_branch')
              HEAD_BRANCH=$(echo "$ADDITIONAL_INFO" | jq -r '.head_branch')
              ADDITIONAL_CONTENT="<br />Branch: $HEAD_BRANCH → $BASE_BRANCH"
            fi
            if echo "$ADDITIONAL_INFO" | jq -e '.status' > /dev/null 2>&1; then
              STATUS=$(echo "$ADDITIONAL_INFO" | jq -r '.status')
              ADDITIONAL_CONTENT="$ADDITIONAL_CONTENT<br />Trạng thái: $STATUS"
            fi
          fi

          # Build final content
          FINAL_CONTENT="$BASE_CONTENT$ADDITIONAL_CONTENT<br />Repository: ${{ github.repository }}<br />Workflow: [Xem chi tiết]($WORKFLOW_URL)<br /><br />$ACTION_CONTENT"
          
          # Custom content override
          CONTENT_INPUT="${{ inputs.content }}"
          if [[ -n "$CONTENT_INPUT" ]] && [[ "$CONTENT_INPUT" != "auto" ]]; then
            FINAL_CONTENT="$CONTENT_INPUT"
          fi

          echo "event_type=$EVENT_TYPE" >> $GITHUB_OUTPUT
          echo "final_content<<EOF" >> $GITHUB_OUTPUT
          echo "$FINAL_CONTENT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Send notification to Lark
        env:
          LARK_WEBHOOK_URL: ${{ secrets.LARK_WEBHOOK_URL }}
        run: |
          echo "✅ Sending notification to Lark webhook..."
          
          EVENT_TYPE="${{ steps['build-content'].outputs.event_type }}"
          FINAL_CONTENT="${{ steps['build-content'].outputs.final_content }}"
          
          echo "Event Type: $EVENT_TYPE"
          echo "Content preview: ${FINAL_CONTENT:0:200}..."
          
          # Create JSON payload
          JSON_PAYLOAD=$(jq -n \
            --arg content "$FINAL_CONTENT" \
            --arg eventType "$EVENT_TYPE" \
            '{
              msg_type: "interactive",
              card: {
                config: { wide_screen_mode: true },
                elements: [
                  { tag: "markdown", content: $content }
                ],
                header: { template: ${{ toJSON(inputs.header_template) }}, title: { content: $eventType, tag: "plain_text" } }
              }
            }')

          # Send notification
          if [[ -n "$LARK_WEBHOOK_URL" ]]; then
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
              -H "Content-Type: application/json" \
              -d "$JSON_PAYLOAD" \
              "$LARK_WEBHOOK_URL")
            
            if [[ "$HTTP_STATUS" -ge 200 && "$HTTP_STATUS" -lt 300 ]]; then
              echo "✅ Notification sent successfully! (Status: $HTTP_STATUS)"
            else
              echo "❌ Failed to send notification. HTTP Status: $HTTP_STATUS"
              exit 1
            fi
          else
            echo "❌ LARK_WEBHOOK_URL is not configured. Skipping notification."
            exit 1
          fi

          echo "🎉 Lark notification process completed!"