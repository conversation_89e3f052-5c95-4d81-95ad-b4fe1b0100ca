# GitHub Workflows - Flexible Runner System

## Tổng quan

Hệ thống workflows đã được refactor để sử dụng **Flexible Runner** và **Reusable Workflows**, giảm thiểu code duplication và tăng tính bảo trì.

## Kiến trúc

### 🔄 Reusable Workflows

#### 1. `reusable-flexible-runner.yml`
Workflow tái sử dụng chính cho việc setup môi trường và chạy jobs với flexible runner.

**Tính năng:**
- ✅ Flexible runner selection (input > repository variable > fallback)
- ✅ Conditional setup (Java, Node.js, PNPM)
- ✅ Dependency caching
- ✅ Automatic dependency installation
- ✅ Custom working directory
- ✅ Custom commands execution

**Inputs:**
```yaml
runner_choice: 'ubuntu-latest|macos-latest'  # Chọn runner
job_name: 'Tên job'                          # Tên hiển thị
setup_java: true/false                       # Setup Java
java_version: '21'                          # Phiên bản Java
setup_node: true/false                      # Setup Node.js
node_version: '20'                          # Phiên bản Node.js
setup_pnpm: true/false                      # Setup PNPM
pnpm_version: '10'                          # Phiên bản PNPM
working_directory: '.'                       # Thư mục làm việc
run_command: 'echo "Hello"'                 # Command chạy
install_dependencies: 'none|frontend|backend|both'  # Cài dependencies
```

#### 2. `reusable-lark-notification.yml`
Workflow tái sử dụng cho việc gửi notification tới Lark.

**Tính năng:**
- ✅ Multiple notification types (PR, Issue, Requirements)
- ✅ Auto content generation
- ✅ Custom header templates
- ✅ Timezone formatting (GMT+7)
- ✅ Project code integration

**Inputs:**
```yaml
notification_type: 'pr|issue|requirements'   # Loại thông báo
title: 'Tiêu đề'                           # Tiêu đề
event_url: 'https://...'                    # URL event
event_number: '123'                         # Số PR/Issue
event_user: 'username'                      # Người tạo
event_created_at: '2024-01-01T00:00:00Z'  # Thời gian tạo
additional_info: '{...}'                    # JSON info bổ sung
header_template: 'blue|orange|red|green'   # Màu header
```

### 🔧 Refactored Workflows

#### 1. `flexible-runner.yml` ➜ **Fully Refactored**
- ✅ Sử dụng `reusable-flexible-runner.yml`
- ✅ Thêm job type selection
- ✅ Dynamic environment setup

#### 2. `copilot-setup-steps.yml` ➜ **Fully Refactored**
- ✅ Sử dụng `reusable-flexible-runner.yml`
- ✅ Full-stack setup (Java + Node.js + PNPM)
- ✅ Dependencies installation

#### 3. `pr-notification.yml` ➜ **Fully Refactored**
- ✅ Sử dụng `reusable-lark-notification.yml`
- ✅ Auto PR information extraction
- ✅ Blue header template

#### 4. `requirements-change-notification.yml` ➜ **Fully Refactored**
- ✅ Sử dụng `reusable-lark-notification.yml`
- ✅ Orange header template for requirements
- ✅ Issue-specific content

#### 5. `ci-build.yml` ➜ **Partially Refactored**
- ✅ Build jobs sử dụng `reusable-flexible-runner.yml`
- ✅ Flexible runner cho tất cả jobs
- ⚠️ Giữ nguyên `detect-changes` job (cần outputs)

#### 6. `requirements-change-review.yml` ➜ **Minimal Refactored**
- ✅ Flexible runner
- ⚠️ Giữ nguyên logic phức tạp (GitHub script)

## 🎯 Lợi ích

### 1. **Giảm Code Duplication**
- **Trước:** ~350 lines code trùng lặp cho setup môi trường
- **Sau:** ~50 lines per workflow, reuse 1 central workflow

### 2. **Flexible Runner Support**
- **Auto-detection:** `workflow_dispatch` input ➜ Repository variable ➜ Fallback
- **Consistent:** Tất cả workflows đều support flexible runner
- **Configurable:** Dễ dàng thay đổi runner mặc định

### 3. **Easier Maintenance**
- **Centralized Logic:** Setup logic tập trung trong reusable workflows
- **Version Control:** Dễ dàng update dependencies, tools
- **Testing:** Test 1 lần cho tất cả workflows

### 4. **Better Notification System**
- **Unified:** Tất cả notifications đều có format consistent
- **Extensible:** Dễ dàng thêm notification types mới
- **Customizable:** Support multiple templates và content types

## 📋 Usage Examples

### Sử dụng Flexible Runner

```yaml
jobs:
  my-job:
    uses: ./.github/workflows/reusable-flexible-runner.yml
    with:
      job_name: 'My Custom Job'
      setup_java: true
      setup_node: true
      setup_pnpm: true
      install_dependencies: 'both'
      run_command: |
        echo "Running custom commands..."
        npm run build
        mvn clean install
```

### Sử dụng Lark Notification

```yaml
jobs:
  notify:
    uses: ./.github/workflows/reusable-lark-notification.yml
    with:
      notification_type: 'pr'
      title: ${{ github.event.pull_request.title }}
      event_url: ${{ github.event.pull_request.html_url }}
      event_number: ${{ github.event.pull_request.number }}
      event_user: ${{ github.event.pull_request.user.login }}
      event_created_at: ${{ github.event.pull_request.created_at }}
      header_template: 'blue'
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_CHAT_GROUP_NOTIFICATION }}
```

## ⚙️ Configuration

### Repository Variables
- `DEFAULT_RUNNER_LABEL`: Runner mặc định (vd: `ubuntu-latest`)
- `PROJECT_CODE`: Mã dự án cho notifications

### Repository Secrets
- `LARK_CHAT_GROUP_NOTIFICATION`: Lark webhook URL

## 🔍 Migration Status

| Workflow | Status | Notes |
|----------|--------|-------|
| `flexible-runner.yml` | ✅ **Complete** | Fully refactored |
| `copilot-setup-steps.yml` | ✅ **Complete** | Fully refactored |
| `pr-notification.yml` | ✅ **Complete** | Fully refactored |
| `requirements-change-notification.yml` | ✅ **Complete** | Fully refactored |
| `ci-build.yml` | 🟡 **Partial** | Build jobs refactored, detection job kept |
| `requirements-change-review.yml` | 🟡 **Minimal** | Only runner made flexible |

## 🚀 Next Steps

1. **Test các workflows** trong development environment
2. **Monitor performance** của reusable workflows
3. **Extend reusable workflows** với thêm options nếu cần
4. **Documentation** cho developers về cách sử dụng

---

**🤖 Generated with Claude Code**