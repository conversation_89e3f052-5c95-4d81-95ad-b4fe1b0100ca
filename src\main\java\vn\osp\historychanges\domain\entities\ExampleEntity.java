package vn.osp.historychanges.domain.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.osp.common.domain.domainentitytypes.BaseDomainEntityUuid;
import vn.osp.common.domain.domainentitytypes.HasCreatedBy;
import vn.osp.common.domain.domainentitytypes.HasModifiedBy;

@Entity
@Table(name = "\"ExampleEntities\"")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ExampleEntity extends BaseDomainEntityUuid implements HasCreatedBy, HasModifiedBy {

  @Column(name = "\"Title\"", nullable = false, length = 200)
  private String title;

  @Column(name = "\"Link\"", nullable = false, length = 500)
  private String link;

  @Column(name = "\"IdPath\"", length = 1000)
  private String idPath;

  @Column(name = "\"ActiveLink\"", length = 500)
  private String activeLink;

  @Column(name = "\"ParentId\"")
  private UUID parentId;

  @Column(name = "\"Icon\"", length = 100)
  private String icon;

  @Column(name = "\"AppCode\"", nullable = false, length = 50)
  private String appCode;

  @Column(name = "\"IsVisible\"", nullable = false)
  private Boolean isVisible;

  @Column(name = "\"OrderIndex\"", nullable = false)
  private Integer order;

  @Column(name = "\"RequiredRoles\"", length = 1000)
  private String requiredRoles;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumn(name = "\"Children\"")
  private List<ExampleEntity> children = new ArrayList<>();

  @Column(name = "\"Created\"", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "\"CreatedBy\"", nullable = false)
  private UUID createdBy;

  @Column(name = "\"Modified\"", nullable = false)
  private LocalDateTime modifiedAt;

  @Column(name = "\"ModifiedBy\"", nullable = false)
  private UUID modifiedBy;
}
